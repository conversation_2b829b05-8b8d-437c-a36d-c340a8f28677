#include "pullze.h"
#include <iostream>
#include <iomanip>
using namespace std;

void initializePuzzle(Puzzle& puzzle, int rows, int cols) {
    puzzle.rows = rows;
    puzzle.cols = cols;

    // Initialize grid with random balls
    for (int i = 0; i < rows; i++) {
        for (int j = 0; j < cols; j++) {
            puzzle.grid[i][j] = (rand() % 2 == 0);  // Random true/false
            puzzle.marked[i][j] = false;            // Initially unmarked
        }
    }

    // Generate hints based on the grid
    generateHints(puzzle);
}

void generateHints(Puzzle& puzzle) {
    // Generate row hints
    for (int i = 0; i < puzzle.rows; i++) {
        int count = 0;
        int hintIndex = 0;

        for (int j = 0; j < puzzle.cols; j++) {
            if (puzzle.grid[i][j]) {
                count++;
            }
            else if (count > 0) {
                puzzle.rowHints[i][hintIndex++] = count;
                count = 0;
            }
        }

        if (count > 0) {
            puzzle.rowHints[i][hintIndex++] = count;
        }

        puzzle.rowHintCount[i] = hintIndex;
        if (hintIndex == 0) {
            puzzle.rowHints[i][0] = 0;
            puzzle.rowHintCount[i] = 1;
        }
    }

    // Generate column hints
    for (int j = 0; j < puzzle.cols; j++) {
        int count = 0;
        int hintIndex = 0;

        for (int i = 0; i < puzzle.rows; i++) {
            if (puzzle.grid[i][j]) {
                count++;
            }
            else if (count > 0) {
                puzzle.colHints[j][hintIndex++] = count;
                count = 0;
            }
        }

        if (count > 0) {
            puzzle.colHints[j][hintIndex++] = count;
        }

        puzzle.colHintCount[j] = hintIndex;
        if (hintIndex == 0) {
            puzzle.colHints[j][0] = 0;
            puzzle.colHintCount[j] = 1;
        }
    }
}
void printTextPuzzle(const Puzzle& puzzle) {
    // Print column headers
    cout << "  ";
    for (int j = 0; j < puzzle.cols; j++) {
        if (j > 0 && j % 5 == 0) {
            cout << "| ";
        }
        cout << static_cast<char>('a' + j) << " ";
    }
    cout << endl;

    // Print separator line
    cout << "  ";
    for (int j = 0; j < puzzle.cols; j++) {
        if (j > 0 && j % 5 == 0) {
            cout << "+ ";
        }
        cout << "- ";
    }
    cout << endl;

    // Print grid
    for (int i = 0; i < puzzle.rows; i++) {
        // Print row header
        cout << static_cast<char>('A' + i) << " ";

        // Print row content
        for (int j = 0; j < puzzle.cols; j++) {
            if (j > 0 && j % 5 == 0) {
                cout << "| ";
            }
            cout << (puzzle.grid[i][j] ? "O " : "  ");
        }
        cout << endl;

        // Print separator line every 5 rows
        if ((i + 1) % 5 == 0 && i < puzzle.rows - 1) {
            cout << "  ";
            for (int j = 0; j < puzzle.cols; j++) {
                if (j > 0 && j % 5 == 0) {
                    cout << "+ ";
                }
                cout << "- ";
            }
            cout << endl;
        }
    }
}

void printTextPuzzleWithHints(const Puzzle& puzzle) {
    // Find maximum number of hints for rows to calculate padding
    int maxRowHints = 0;
    for (int i = 0; i < puzzle.rows; i++) {
        if (puzzle.rowHintCount[i] > maxRowHints) {
            maxRowHints = puzzle.rowHintCount[i];
        }
    }

    // Print column hints
    for (int hintRow = 0; hintRow < puzzle.colHintCount[0]; hintRow++) {
        // Print padding for row hints area
        cout << string(maxRowHints * 2 + 2, ' ');

        // Print column hints
        for (int j = 0; j < puzzle.cols; j++) {
            if (j > 0 && j % 5 == 0) {
                cout << "| ";
            }

            int hintIndex = puzzle.colHintCount[j] - 1 - hintRow;
            if (hintIndex >= 0) {
                cout << puzzle.colHints[j][hintIndex] << " ";
            }
            else {
                cout << "  ";
            }
        }
        cout << endl;
    }

    // Print column headers
    cout << string(maxRowHints * 2, ' ') << "  ";
    for (int j = 0; j < puzzle.cols; j++) {
        if (j > 0 && j % 5 == 0) {
            cout << "| ";
        }
        cout << static_cast<char>('a' + j) << " ";
    }
    cout << endl;

    // Print separator line
    cout << string(maxRowHints * 2, ' ') << "  ";
    for (int j = 0; j < puzzle.cols; j++) {
        if (j > 0 && j % 5 == 0) {
            cout << "+ ";
        }
        cout << "- ";
    }
    cout << endl;

    // Print grid with row hints
    for (int i = 0; i < puzzle.rows; i++) {
        // Print row hints
        for (int h = 0; h < maxRowHints; h++) {
            int hintIndex = maxRowHints - 1 - h;
            if (hintIndex < puzzle.rowHintCount[i]) {
                cout << puzzle.rowHints[i][hintIndex] << " ";
            }
            else {
                cout << "  ";
            }
        }

        // Print row header
        cout << static_cast<char>('A' + i) << " ";

        // Print row content
        for (int j = 0; j < puzzle.cols; j++) {
            if (j > 0 && j % 5 == 0) {
                cout << "| ";
            }
            cout << (puzzle.grid[i][j] ? "O " : "  ");
        }
        cout << endl;

        // Print separator line every 5 rows
        if ((i + 1) % 5 == 0 && i < puzzle.rows - 1) {
            cout << string(maxRowHints * 2, ' ') << "  ";
            for (int j = 0; j < puzzle.cols; j++) {
                if (j > 0 && j % 5 == 0) {
                    cout << "+ ";
                }
                cout << "- ";
            }
            cout << endl;
        }
    }
}