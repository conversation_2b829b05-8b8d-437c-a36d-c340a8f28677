#include "pullze.h"
#include <iostream>
#include <cctype>
using namespace std;

// Get valid input for rows/columns
int getValidInput(const string& prompt, int min, int max) {
    int value;
    while (true) {
        cout << prompt;
        cin >> value;

        if (cin.fail()) {
            cin.clear();
            cin.ignore(10000, '\n');
            cout << "Invalid input. Please enter a number." << endl;
        }
        else if (value < min || value > max) {
            cout << "Value must be between " << min << " and " << max << "." << endl;
        }
        else {
            cin.ignore(10000, '\n'); // Clear input buffer
            return value;
        }
    }
}

// Parse coordinates from user input (e.g., "A5" -> row 0, col 4)
bool parseCoordinates(const string& input, int& row, int& col, int maxRow, int maxCol) {
    if (input.length() < 2) {
        return false;
    }

    // Parse row (first character, uppercase letter)
    char rowChar = toupper(input[0]);
    if (rowChar < 'A' || rowChar > 'A' + maxRow - 1) {
        return false;
    }
    row = rowChar - 'A';

    // Parse column (second character, lowercase letter)
    char colChar = tolower(input[1]);
    if (colChar < 'a' || colChar > 'a' + maxCol - 1) {
        return false;
    }
    col = colChar - 'a';

    return true;
}

// Play the text-based game
void playTextGame(Puzzle& puzzle) {
    bool gameRunning = true;

    // Initially show only the hints
    cout << "Game started! Enter coordinates to mark a position (e.g., Aa)." << endl;
    cout << "Enter the same coordinates again to unmark. Enter 'q' to quit." << endl;

    // Print initial state with hints but empty grid
    // Create a temporary puzzle with all cells empty for display
    Puzzle displayPuzzle = puzzle;
    for (int i = 0; i < puzzle.rows; i++) {
        for (int j = 0; j < puzzle.cols; j++) {
            displayPuzzle.grid[i][j] = false;
        }
    }

    printTextPuzzleWithHints(displayPuzzle);

    while (gameRunning) {
        string input;
        cout << "Enter coordinates (or 'q' to quit): ";
        cin >> input;

        if (input == "q" || input == "Q") {
            gameRunning = false;
            continue;
        }

        int row, col;
        if (parseCoordinates(input, row, col, puzzle.rows, puzzle.cols)) {
            // Toggle mark
            puzzle.marked[row][col] = !puzzle.marked[row][col];

            // Update display
            cout << "Position " << input << " "
                << (puzzle.marked[row][col] ? "marked" : "unmarked") << "." << endl;

            // Show current state (cheat mode)
            cout << "Current state (cheat mode):" << endl;
            for (int i = 0; i < puzzle.rows; i++) {
                for (int j = 0; j < puzzle.cols; j++) {
                    if (puzzle.grid[i][j] && puzzle.marked[i][j]) {
                        cout << "O "; // Ball exists and is marked
                    }
                    else if (puzzle.grid[i][j] && !puzzle.marked[i][j]) {
                        cout << "o "; // Ball exists but not marked
                    }
                    else if (!puzzle.grid[i][j] && puzzle.marked[i][j]) {
                        cout << "X "; // No ball but marked (error)
                    }
                    else {
                        cout << "  "; // No ball and not marked
                    }
                }
                cout << endl;
            }
        }
        else {
            cout << "Invalid coordinates. Please use format like 'Aa'." << endl;
        }
    }
}