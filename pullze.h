#pragma once
#include <string>

// Constants
const int MAX_SIZE = 30; // Maximum puzzle size

// Structure to represent the puzzle
struct Puzzle {
    int rows;
    int cols;
    bool grid[MAX_SIZE][MAX_SIZE];     // True if a ball exists
    bool marked[MAX_SIZE][MAX_SIZE];   // True if marked by player
    int rowHints[MAX_SIZE][MAX_SIZE];  // Row hints
    int rowHintCount[MAX_SIZE];        // Number of hints per row
    int colHints[MAX_SIZE][MAX_SIZE];  // Column hints
    int colHintCount[MAX_SIZE];        // Number of hints per column
};

// Function declarations for base operations
void initializePuzzle(Puzzle& puzzle, int rows, int cols);
void generateHints(Puzzle& puzzle);

// Function declarations for text interface
void printTextPuzzle(const Puzzle& puzzle);
void printTextPuzzleWithHints(const Puzzle& puzzle);
int getValidInput(const std::string& prompt, int min, int max);
bool parseCoordinates(const std::string& input, int& row, int& col, int maxRow, int maxCol);
void playTextGame(Puzzle& puzzle);

// Function declarations for graphic interface
void drawGraphicPuzzle(const Puzzle& puzzle, bool withSeparators);
void drawGraphicPuzzleWithHints(const Puzzle& puzzle, bool withSeparators);
bool handleMouseInput(const Puzzle& puzzle, bool withSeparators, int& row, int& col);
void playGraphicGame(Puzzle& puzzle, bool withSeparators);