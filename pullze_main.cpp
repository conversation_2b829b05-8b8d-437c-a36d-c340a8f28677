#include "pullze.h"
#include <iostream>
#include <ctime>
#include <cstdlib>
// Remove this line: #include "pullze_tools.cpp"
using namespace std;

int main() {
    // Seed random number generator
    srand(static_cast<unsigned int>(time(nullptr)));

    char choice;
    bool running = true;

    while (running) {
        // Display menu
        cout << "---------------------------------------------------------" << endl;
        cout << "A. Text interface, initial state" << endl;
        cout << "B. Text interface, show with row/column hints" << endl;
        cout << "C. Text interface, play game" << endl;
        cout << "---------------------------------------------------------" << endl;
        cout << "D. Graphic interface without separators, initial state" << endl;
        cout << "E. Graphic interface without separators, show with hints" << endl;
        cout << "F. Graphic interface without separators, mouse movement" << endl;
        cout << "G. Graphic interface without separators, full game" << endl;
        cout << "---------------------------------------------------------" << endl;
        cout << "H. Graphic interface with separators, initial state" << endl;
        cout << "I. Graphic interface with separators, show with hints" << endl;
        cout << "J. Graphic interface with separators, mouse movement" << endl;
        cout << "K. Graphic interface with separators, full game" << endl;
        cout << "---------------------------------------------------------" << endl;
        cout << "Q. Quit" << endl;
        cout << "---------------------------------------------------------" << endl;
        cout << "[Choose:] ";

        cin >> choice;
        choice = toupper(choice);
        cin.ignore(10000, '\n');

        if (choice >= 'A' && choice <= 'K' || choice == 'Q') {
            // Valid choice
            if (choice == 'Q') {
                running = false;
                continue;
            }

            // Get puzzle dimensions
            int rows = getValidInput("Enter number of rows (5-20): ", 5, 20);
            int cols = getValidInput("Enter number of columns (5-20): ", 5, 20);

            // Initialize puzzle
            Puzzle puzzle;
            initializePuzzle(puzzle, rows, cols);

            // Process choice
            switch (choice) {
                case 'A':
                    printTextPuzzle(puzzle);
                    break;
                case 'B':
                    printTextPuzzleWithHints(puzzle);
                    break;
                case 'C':
                    playTextGame(puzzle);
                    break;
                case 'D': {
                    // Graphic interface without separators, initial state
                    drawGraphicPuzzle(puzzle, false);
                    cout << "\nPress any key to continue...";
                    cin.get();
                    break;
                }
                case 'E': {
                    // Graphic interface without separators, show with hints
                    drawGraphicPuzzleWithHints(puzzle, false);
                    cout << "\nPress any key to continue...";
                    cin.get();
                    break;
                }
                case 'F': {
                    // Graphic interface without separators, mouse movement
                    // Create a display puzzle (initially empty)
                    Puzzle displayPuzzle = puzzle;
                    for (int i = 0; i < puzzle.rows; i++) {
                        for (int j = 0; j < puzzle.cols; j++) {
                            displayPuzzle.grid[i][j] = false;
                        }
                    }
                    drawGraphicPuzzleWithHints(displayPuzzle, false);

                    int row, col;
                    handleMouseInput(displayPuzzle, false, row, col);
                    break;
                }
                case 'G': {
                    // Graphic interface without separators, full game
                    playGraphicGame(puzzle, false);
                    break;
                }
                case 'H': {
                    // Graphic interface with separators, initial state
                    drawGraphicPuzzle(puzzle, true);
                    cout << "\nPress any key to continue...";
                    cin.get();
                    break;
                }
                case 'I': {
                    // Graphic interface with separators, show with hints
                    drawGraphicPuzzleWithHints(puzzle, true);
                    cout << "\nPress any key to continue...";
                    cin.get();
                    break;
                }
                case 'J': {
                    // Graphic interface with separators, mouse movement
                    // Create a display puzzle (initially empty)
                    Puzzle displayPuzzle = puzzle;
                    for (int i = 0; i < puzzle.rows; i++) {
                        for (int j = 0; j < puzzle.cols; j++) {
                            displayPuzzle.grid[i][j] = false;
                        }
                    }
                    drawGraphicPuzzleWithHints(displayPuzzle, true);

                    int row, col;
                    handleMouseInput(displayPuzzle, true, row, col);
                    break;
                }
                case 'K': {
                    // Graphic interface with separators, full game
                    playGraphicGame(puzzle, true);
                    break;
                }
                case 'Q':
                case 'q':
                    running = false;
                    break;
                default:
                    cout << "Invalid choice. Please try again." << endl;
                    break;
            }
        }

        return 0;
    }
}